<documentation title="Uppercase PHP Constants">
    <standard>
    <![CDATA[
    The <em>true</em>, <em>false</em> and <em>null</em> constants must always be uppercase.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Uppercase constants.">
        <![CDATA[
if ($var === <em>FALSE</em> || $var === <em>NULL</em>) {
    $var = <em>TRUE</em>;
}
        ]]>
        </code>
        <code title="Invalid: Lowercase constants.">
        <![CDATA[
if ($var === <em>false</em> || $var === <em>null</em>) {
    $var = <em>true</em>;
}
        ]]>
        </code>
    </code_comparison>
</documentation>
