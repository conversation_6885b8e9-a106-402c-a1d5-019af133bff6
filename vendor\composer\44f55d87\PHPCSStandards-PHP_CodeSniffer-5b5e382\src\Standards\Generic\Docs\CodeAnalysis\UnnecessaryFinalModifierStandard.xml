<documentation title="Unnecessary Final Modifiers">
    <standard>
    <![CDATA[
    Methods should not be declared final inside of classes that are declared final.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A method in a final class is not marked final.">
        <![CDATA[
final class Foo
{
    public function bar()
    {
    }
}
        ]]>
        </code>
        <code title="Invalid: A method in a final class is also marked final.">
        <![CDATA[
final class Foo
{
    public <em>final</em> function bar()
    {
    }
}
        ]]>
        </code>
    </code_comparison>
</documentation>
