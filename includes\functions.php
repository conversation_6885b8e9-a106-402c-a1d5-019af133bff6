<?php
/**
 * الدوال المساعدة العامة
 * General Helper Functions
 */

require_once __DIR__ . '/../config/database.php';

/**
 * تنظيف البيانات المدخلة
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * التحقق من تسجيل الدخول
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من صلاحيات الأدمن
 */
function is_admin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * إعادة التوجيه
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * عرض رسالة تنبيه
 */
function show_alert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * الحصول على رسالة التنبيه وحذفها
 */
function get_alert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

/**
 * تسجيل العمليات
 */
function log_activity($user_id, $action, $table_name, $record_id = null, $old_values = null, $new_values = null) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $query = "INSERT INTO activity_log (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                  VALUES (:user_id, :action, :table_name, :record_id, :old_values, :new_values, :ip_address, :user_agent)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':table_name', $table_name);
        $stmt->bindParam(':record_id', $record_id);
        $stmt->bindParam(':old_values', json_encode($old_values, JSON_UNESCAPED_UNICODE));
        $stmt->bindParam(':new_values', json_encode($new_values, JSON_UNESCAPED_UNICODE));
        $stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
        $stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT']);
        
        $stmt->execute();
    } catch(Exception $e) {
        error_log("خطأ في تسجيل العملية: " . $e->getMessage());
    }
}

/**
 * تشفير كلمة المرور
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * توليد رمز عشوائي
 */
function generate_token($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * تنسيق التاريخ للعرض
 */
function format_date($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * تنسيق المبلغ للعرض
 */
function format_currency($amount) {
    return number_format($amount, 2) . ' جنيه';
}

/**
 * التحقق من صحة الرقم القومي المصري
 */
function validate_national_id($national_id) {
    // التحقق من أن الرقم يتكون من 14 رقم
    if (!preg_match('/^\d{14}$/', $national_id)) {
        return false;
    }
    
    // التحقق من صحة تاريخ الميلاد
    $century = substr($national_id, 0, 1);
    $year = substr($national_id, 1, 2);
    $month = substr($national_id, 3, 2);
    $day = substr($national_id, 5, 2);
    
    if ($century == '2') {
        $year = '19' . $year;
    } elseif ($century == '3') {
        $year = '20' . $year;
    } else {
        return false;
    }
    
    return checkdate($month, $day, $year);
}

/**
 * التحقق من صحة رقم الهاتف المصري
 */
function validate_phone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // التحقق من أن الرقم يبدأ بـ 01 ويتكون من 11 رقم
    return preg_match('/^01[0-9]{9}$/', $phone);
}

/**
 * تحويل النص إلى UTF-8
 */
function ensure_utf8($text) {
    if (!mb_check_encoding($text, 'UTF-8')) {
        return mb_convert_encoding($text, 'UTF-8', 'auto');
    }
    return $text;
}
?>
