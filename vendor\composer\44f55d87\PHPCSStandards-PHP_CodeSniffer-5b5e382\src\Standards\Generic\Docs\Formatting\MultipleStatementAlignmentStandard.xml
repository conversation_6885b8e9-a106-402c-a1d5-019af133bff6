<documentation title="Aligning Blocks of Assignments">
    <standard>
    <![CDATA[
    There should be one space on either side of an equals sign used to assign a value to a variable. In the case of a block of related assignments, more space may be inserted to promote readability.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Equals signs aligned.">
        <![CDATA[
$shortVar        <em>=</em> (1 + 2);
$veryLongVarName <em>=</em> 'string';
$var             <em>=</em> foo($bar, $baz);
        ]]>
        </code>
        <code title="Invalid: Not aligned; harder to read.">
        <![CDATA[
$shortVar <em>=</em> (1 + 2);
$veryLongVarName <em>=</em> 'string';
$var <em>=</em> foo($bar, $baz);
        ]]>
        </code>
    </code_comparison>
    <standard>
    <![CDATA[
    When using plus-equals, minus-equals etc. still ensure the equals signs are aligned to one space after the longest variable name.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Equals signs aligned; only one space after longest var name.">
        <![CDATA[
$shortVar       <em>+= </em>1;
$veryLongVarName<em> = </em>1;
        ]]>
        </code>
        <code title="Invalid: Two spaces after longest var name.">
        <![CDATA[
$shortVar       <em> += </em>1;
$veryLongVarName<em>  = </em>1;
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Equals signs aligned.">
        <![CDATA[
$shortVar       <em>  = </em>1;
$veryLongVarName<em> -= </em>1;
        ]]>
        </code>
        <code title="Invalid: Equals signs not aligned.">
        <![CDATA[
$shortVar       <em> = </em>1;
$veryLongVarName<em> -= </em>1;
        ]]>
        </code>
    </code_comparison>
</documentation>
