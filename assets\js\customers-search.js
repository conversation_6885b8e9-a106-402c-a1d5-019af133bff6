/**
 * JavaScript لنظام البحث الديناميكي في العملاء
 * Dynamic Customer Search JavaScript
 */

let currentPage = 1;
let totalPages = 1;
let searchTimeout;

$(document).ready(function() {
    // تحميل البيانات الأولية
    searchCustomers();
    
    // إضافة مستمعي الأحداث للبحث التلقائي
    $('#search_text').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(searchCustomers, 500);
    });
    
    // إضافة مستمعي الأحداث للفلاتر
    $('#governorate_filter, #status_filter, #sort_by').on('change', searchCustomers);
    $('#date_from, #date_to, #amount_from, #amount_to').on('change', searchCustomers);
    
    // تفعيل البحث عند الضغط على Enter
    $('#searchForm input').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            searchCustomers();
        }
    });
});

/**
 * البحث في العملاء
 */
function searchCustomers(page = 1) {
    currentPage = page;
    
    // عرض مؤشر التحميل
    showLoading();
    
    // جمع بيانات البحث
    const searchData = {
        search_text: $('#search_text').val(),
        governorate_filter: $('#governorate_filter').val(),
        date_from: $('#date_from').val(),
        date_to: $('#date_to').val(),
        amount_from: $('#amount_from').val(),
        amount_to: $('#amount_to').val(),
        status_filter: $('#status_filter').val(),
        sort_by: $('#sort_by').val(),
        page: page,
        limit: 20
    };
    
    $.ajax({
        url: 'api/search_customers.php',
        method: 'GET',
        data: searchData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                displayResults(response.customers);
                updatePagination(response.pagination);
                updateResultsCount(response.pagination.total);
            } else {
                showError('حدث خطأ في البحث: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في AJAX:', error);
            showError('حدث خطأ في الاتصال بالخادم');
        }
    });
}

/**
 * عرض النتائج في الجدول
 */
function displayResults(customers) {
    const tbody = $('#customers-tbody');
    tbody.empty();
    
    if (customers.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="9" class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3 d-block"></i>
                    <h5 class="text-muted">لا توجد نتائج</h5>
                    <p class="text-muted">جرب تغيير معايير البحث</p>
                </td>
            </tr>
        `);
        return;
    }
    
    customers.forEach(customer => {
        const statusBadge = getStatusBadge(customer.end_date);
        const row = `
            <tr>
                <td>
                    <strong>${customer.name}</strong>
                    <br>
                    <small class="text-muted">${customer.account_card}</small>
                </td>
                <td>
                    <span class="badge bg-secondary">${customer.national_id}</span>
                </td>
                <td>
                    <i class="fas fa-map-marker-alt me-1 text-primary"></i>
                    ${customer.governorate || 'غير محدد'}
                </td>
                <td>
                    <span class="fw-bold text-success">${formatCurrency(customer.amount)}</span>
                </td>
                <td>
                    <span class="fw-bold text-info">${formatCurrency(customer.total)}</span>
                </td>
                <td>
                    <small>${formatDate(customer.start_date)}</small>
                </td>
                <td>
                    <small>${formatDate(customer.end_date)}</small>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewCustomer(${customer.id})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editCustomer(${customer.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="manageFiles(${customer.id})" title="إدارة الملفات">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="printCustomer(${customer.id})" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

/**
 * تحديث أزرار الصفحات
 */
function updatePagination(pagination) {
    totalPages = pagination.total_pages;
    currentPage = pagination.current_page;
    
    const paginationContainer = $('#pagination-container');
    const paginationList = $('#pagination');
    
    if (totalPages <= 1) {
        paginationContainer.hide();
        return;
    }
    
    paginationContainer.show();
    paginationList.empty();
    
    // زر الصفحة السابقة
    const prevDisabled = currentPage === 1 ? 'disabled' : '';
    paginationList.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" onclick="searchCustomers(${currentPage - 1})" aria-label="السابق">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `);
    
    // أرقام الصفحات
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        paginationList.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="searchCustomers(1)">1</a>
            </li>
        `);
        if (startPage > 2) {
            paginationList.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        const active = i === currentPage ? 'active' : '';
        paginationList.append(`
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="searchCustomers(${i})">${i}</a>
            </li>
        `);
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationList.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
        paginationList.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="searchCustomers(${totalPages})">${totalPages}</a>
            </li>
        `);
    }
    
    // زر الصفحة التالية
    const nextDisabled = currentPage === totalPages ? 'disabled' : '';
    paginationList.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" onclick="searchCustomers(${currentPage + 1})" aria-label="التالي">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `);
}

/**
 * تحديث عداد النتائج
 */
function updateResultsCount(total) {
    $('#results-count').text(`${total.toLocaleString('ar-EG')} نتيجة`);
}

/**
 * الحصول على شارة الحالة
 */
function getStatusBadge(endDate) {
    const today = new Date();
    const end = new Date(endDate);
    
    if (end >= today) {
        return '<span class="badge bg-success">نشط</span>';
    } else {
        return '<span class="badge bg-danger">منتهي</span>';
    }
}

/**
 * تنسيق المبلغ
 */
function formatCurrency(amount) {
    if (amount == null || amount === '') return '0.00 جنيه';
    
    let number = parseFloat(amount);
    if (isNaN(number)) return '0.00 جنيه';
    
    return number.toLocaleString('ar-EG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }) + ' جنيه';
}

/**
 * تنسيق التاريخ
 */
function formatDate(dateString) {
    if (!dateString) return '';
    
    let date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    
    return date.toLocaleDateString('ar-EG');
}

/**
 * عرض مؤشر التحميل
 */
function showLoading() {
    $('#customers-tbody').html(`
        <tr>
            <td colspan="9" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <div class="mt-2">جاري البحث...</div>
            </td>
        </tr>
    `);
}

/**
 * عرض رسالة خطأ
 */
function showError(message) {
    $('#customers-tbody').html(`
        <tr>
            <td colspan="9" class="text-center py-5 text-danger">
                <i class="fas fa-exclamation-triangle fa-3x mb-3 d-block"></i>
                <h5>${message}</h5>
            </td>
        </tr>
    `);
}

/**
 * مسح البحث
 */
function clearSearch() {
    $('#searchForm')[0].reset();
    searchCustomers();
}

/**
 * عرض تفاصيل العميل
 */
function viewCustomer(customerId) {
    $.ajax({
        url: 'api/get_customer.php',
        method: 'GET',
        data: { id: customerId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                displayCustomerDetails(response.customer);
                $('#customerModal').modal('show');
                $('#edit-customer-btn').attr('onclick', `editCustomer(${customerId})`);
            } else {
                alert('خطأ في تحميل بيانات العميل: ' + response.message);
            }
        },
        error: function() {
            alert('حدث خطأ في الاتصال بالخادم');
        }
    });
}

/**
 * تعديل العميل
 */
function editCustomer(customerId) {
    window.location.href = `edit_customer.php?id=${customerId}`;
}

/**
 * إدارة ملفات العميل
 */
function manageFiles(customerId) {
    window.location.href = `customer_files.php?id=${customerId}`;
}

/**
 * طباعة بيانات العميل
 */
function printCustomer(customerId) {
    window.open(`print_customer.php?id=${customerId}`, '_blank');
}

/**
 * عرض تفاصيل العميل في النافذة المنبثقة
 */
function displayCustomerDetails(customer) {
    const statusBadge = getStatusBadge(customer.end_date);

    $('#customer-details').html(`
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary mb-3">البيانات الأساسية</h6>
                <table class="table table-sm">
                    <tr><td><strong>الاسم:</strong></td><td>${customer.name}</td></tr>
                    <tr><td><strong>الرقم القومي:</strong></td><td>${customer.national_id}</td></tr>
                    <tr><td><strong>الحساب/الكارت:</strong></td><td>${customer.account_card}</td></tr>
                    <tr><td><strong>المحافظة:</strong></td><td>${customer.governorate || 'غير محدد'}</td></tr>
                    <tr><td><strong>الهاتف:</strong></td><td>${customer.phone || 'غير محدد'}</td></tr>
                    <tr><td><strong>هاتف آخر:</strong></td><td>${customer.phone2 || 'غير محدد'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary mb-3">التواريخ والمبالغ</h6>
                <table class="table table-sm">
                    <tr><td><strong>التاريخ:</strong></td><td>${formatDate(customer.date)}</td></tr>
                    <tr><td><strong>بداية الصرف:</strong></td><td>${formatDate(customer.start_date)}</td></tr>
                    <tr><td><strong>نهاية الصرف:</strong></td><td>${formatDate(customer.end_date)}</td></tr>
                    <tr><td><strong>المدة:</strong></td><td>${customer.duration_months} شهر</td></tr>
                    <tr><td><strong>المبلغ:</strong></td><td>${formatCurrency(customer.amount)}</td></tr>
                    <tr><td><strong>الإجمالي:</strong></td><td>${formatCurrency(customer.total)}</td></tr>
                    <tr><td><strong>الحالة:</strong></td><td>${statusBadge}</td></tr>
                </table>
            </div>
        </div>

        ${customer.permit_number || customer.file_number || customer.transfer_number ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-primary mb-3">أرقام الوثائق</h6>
                <table class="table table-sm">
                    ${customer.permit_number ? `<tr><td><strong>رقم الإذن:</strong></td><td>${customer.permit_number}</td></tr>` : ''}
                    ${customer.file_number ? `<tr><td><strong>الملف:</strong></td><td>${customer.file_number}</td></tr>` : ''}
                    ${customer.transfer_number ? `<tr><td><strong>رقم التحويل:</strong></td><td>${customer.transfer_number}</td></tr>` : ''}
                </table>
            </div>
        </div>
        ` : ''}

        ${customer.statement || customer.notes ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-primary mb-3">البيان والملاحظات</h6>
                ${customer.statement ? `<p><strong>البيان:</strong><br>${customer.statement}</p>` : ''}
                ${customer.notes ? `<p><strong>ملاحظات:</strong><br>${customer.notes}</p>` : ''}
            </div>
        </div>
        ` : ''}

        <div class="row mt-3">
            <div class="col-12">
                <small class="text-muted">
                    تم الإنشاء: ${formatDate(customer.created_at)} |
                    آخر تحديث: ${formatDate(customer.updated_at)}
                </small>
            </div>
        </div>
    `);
}

/**
 * تصدير النتائج
 */
function exportResults() {
    // جمع بيانات البحث الحالية
    const searchData = {
        search_text: $('#search_text').val(),
        governorate_filter: $('#governorate_filter').val(),
        date_from: $('#date_from').val(),
        date_to: $('#date_to').val(),
        amount_from: $('#amount_from').val(),
        amount_to: $('#amount_to').val(),
        status_filter: $('#status_filter').val(),
        sort_by: $('#sort_by').val(),
        export: 'excel'
    };

    // إنشاء رابط التصدير
    const params = new URLSearchParams(searchData);
    window.open(`api/export_customers.php?${params.toString()}`, '_blank');
}
