<documentation title="Inline HTML">
    <standard>
    <![CDATA[
    Files that contain PHP code should only have PHP code and should not have any "inline html".
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A PHP file with only PHP code in it.">
        <![CDATA[
<?php
$foo = 'bar';
echo $foo . 'baz';
        ]]>
        </code>
        <code title="Invalid: A PHP file with html in it outside of the PHP tags.">
        <![CDATA[
<em>some string here</em>
<?php
$foo = 'bar';
echo $foo . 'baz';
        ]]>
        </code>
    </code_comparison>
</documentation>
