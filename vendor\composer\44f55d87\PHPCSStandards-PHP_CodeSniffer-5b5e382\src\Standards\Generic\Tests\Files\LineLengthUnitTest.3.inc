phpcs:set Generic.Files.LineLength lineLimit 120
phpcs:set Generic.Files.LineLength absoluteLineLimit 150
<?php

/* This line is fine. This line is fine. This line is fine. This line is fine. This line is fine. */
/* This line is too long. This line is too long. This line is too long. This line is too long. This line is too long. This line is too long. */
/* This line is too long. This line is too long. This line is too long. This line is too long. This line is too long. This line is too long. This line is too long. */

            /*
            | ...
            http://symfony.com/doc/current/cookbook/doctrine/dbal.html#registering-custom-mapping-types-in-the-schematoo
            http://symfony.com/doc/current/cookbook/doctrine/dbal.html#registering-custom-mapping-types-in-the-schematool
            http://symfony.com/doc/current/cookbook/doctrine/dbal.html#registering-custom-mapping-types-in-the-schematool1
            http://symfony.com/doc/current/cookbook/doctrine/dbal.html#registering-custom-mapping-types-in-the-schematool12
            | ...
            */
