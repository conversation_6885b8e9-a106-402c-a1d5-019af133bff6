<documentation title="Constant Names">
    <standard>
    <![CDATA[
    Constants should always be all-uppercase, with underscores to separate words.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: All uppercase constant name.">
        <![CDATA[
define('<em>FOO_CONSTANT</em>', 'foo');

class FooClass
{
    const <em>FOO_CONSTANT</em> = 'foo';
}
        ]]>
        </code>
        <code title="Invalid: Mixed case or lowercase constant name.">
        <![CDATA[
define('<em>Foo_Constant</em>', 'foo');

class FooClass
{
    const <em>foo_constant</em> = 'foo';
}
        ]]>
        </code>
    </code_comparison>
</documentation>
