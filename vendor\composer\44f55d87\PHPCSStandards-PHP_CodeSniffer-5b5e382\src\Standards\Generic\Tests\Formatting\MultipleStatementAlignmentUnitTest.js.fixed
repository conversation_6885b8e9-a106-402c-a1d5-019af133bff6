

// Valid
var1    = 'var1';
var10   = 'var1';
var100  = 'var1';
var1000 = 'var1';

// Invalid
var1    = 'var1';
var10   = 'var1';
var100  = 'var1';
var1000 = 'var1';

// Valid
var1  = 'var1';
var10 = 'var1';

var100  = 'var1';
var1000 = 'var1';

// Invalid
var1  = 'var1';
var10 = 'var1';

var100  = 'var1';
var1000 = 'var1';

// Valid
var1    += 'var1';
var10   += 'var1';
var100  += 'var1';
var1000 += 'var1';

// Invalid
var1    += 'var1';
var10   += 'var1';
var100  += 'var1';
var1000 += 'var1';

// Valid
var1     = 'var1';
var10   += 'var1';
var100   = 'var1';
var1000 += 'var1';

// Invalid
var1     = 'var1';
var10   += 'var1';
var100   = 'var1';
var1000 += 'var1';

// Valid
var1  += 'var1';
var10 += 'var1';

var100  += 'var1';
var1000 += 'var1';

// Invalid
var1  += 'var1';
var10 += 'var1';

var100  += 'var1';
var1000 += 'var1';

// Valid
var test = 100;

// InValid
var test = 100;

commentStart = phpcsFile.findPrevious();
commentEnd   = this._phpcsFile;
expected    += '...';

// Invalid
this.okButton = {};
content       = {};

var buttonid         = [this.id, '-positionFormats-add'].join('');
var buttonWidget     = WidgetStore.get(buttonid);
var spinButtonid     = [this.id, '-positionFormats-spinButton'].join('');
var spinButtonWidget = WidgetStore.get(spinButtonid);
var position         = spinButtonWidget.getValue();
var posFormatsList   = WidgetStore.get([self.id, '-positionFormats-list'].join(''));

dfx.stripTags = function(content, allowedTags)
{
    var match;
    var re = 'blah';
};

var contents  += 'if (';
var conditions = array();

var foo  = {};
foo.blah = 'blah';

var script    = document.createElement('script');
script.onload = function()
{
    clearTimeout(t);
};

stream.match(stream.sol() ? /^\s*\/\/.*/ : /^\s+\/\/.*/);
function() {
    if (condition)
        foo = .4
}

x   = x << y;
x <<= y;
x   = x >> y;
x >>= y;

x    = x << y;
x >>>= y;
