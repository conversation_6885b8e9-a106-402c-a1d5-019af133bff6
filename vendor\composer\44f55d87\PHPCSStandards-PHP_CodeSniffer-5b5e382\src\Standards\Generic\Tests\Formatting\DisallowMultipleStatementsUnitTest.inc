<?php
$y = 2;;
echo $y;
for ($i = 1; $i < $length; $i++) {}
for (; $i < $length; $i++) {}
echo 'x'; echo $y;
$x = 10; echo $y;
$this->wizardid = 10; $this->paint(); echo 'x';
?>
<div class="<?php echo $class; ?>" id="<?php echo $id; ?>"></div>
<div class="<?php echo $class ?>" id="<?php echo $id ?>"></div>
<div class="<?= $class; ?>" id="<?= $id; ?>"></div>
<div class="<?= $class ?>" id="<?= $id ?>"></div>

<?php
echo 'x'; /* phpcs:ignore Standard */ echo $y; /* phpcs:ignore OtherStandard */;

for ($i = 0 ; $i < 10; $i++);
{
}
