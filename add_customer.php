<?php
require_once 'includes/functions.php';
require_once 'includes/file_upload.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    redirect('login.php');
}

$database = new Database();
$db = $database->getConnection();

$errors = [];
$success = false;

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // تنظيف البيانات
    $name = sanitize_input($_POST['name'] ?? '');
    $national_id = sanitize_input($_POST['national_id'] ?? '');
    $account_card = sanitize_input($_POST['account_card'] ?? '');
    $date = $_POST['date'] ?? '';
    $start_date = $_POST['start_date'] ?? '';
    $end_date = $_POST['end_date'] ?? '';
    $duration_months = (int)($_POST['duration_months'] ?? 0);
    $amount = (float)($_POST['amount'] ?? 0);
    $total = (float)($_POST['total'] ?? 0);
    $permit_number = sanitize_input($_POST['permit_number'] ?? '');
    $file_number = sanitize_input($_POST['file_number'] ?? '');
    $transfer_number = sanitize_input($_POST['transfer_number'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $phone2 = sanitize_input($_POST['phone2'] ?? '');
    $governorate = sanitize_input($_POST['governorate'] ?? '');
    $statement = sanitize_input($_POST['statement'] ?? '');
    $notes = sanitize_input($_POST['notes'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($name)) {
        $errors[] = 'الاسم مطلوب';
    }
    
    if (empty($national_id)) {
        $errors[] = 'الرقم القومي مطلوب';
    } elseif (!validate_national_id($national_id)) {
        $errors[] = 'الرقم القومي غير صحيح';
    }
    
    if (empty($account_card)) {
        $errors[] = 'رقم الحساب/الكارت مطلوب';
    }
    
    if (empty($date)) {
        $errors[] = 'التاريخ مطلوب';
    }
    
    if (empty($start_date)) {
        $errors[] = 'بداية الصرف مطلوبة';
    }
    
    if (empty($end_date)) {
        $errors[] = 'نهاية الصرف مطلوبة';
    }
    
    if ($duration_months <= 0) {
        $errors[] = 'المدة بالشهور يجب أن تكون أكبر من صفر';
    }
    
    if ($amount <= 0) {
        $errors[] = 'المبلغ يجب أن يكون أكبر من صفر';
    }
    
    if ($total <= 0) {
        $errors[] = 'الإجمالي يجب أن يكون أكبر من صفر';
    }
    
    if (!empty($phone) && !validate_phone($phone)) {
        $errors[] = 'رقم الهاتف الأول غير صحيح';
    }
    
    if (!empty($phone2) && !validate_phone($phone2)) {
        $errors[] = 'رقم الهاتف الثاني غير صحيح';
    }
    
    // التحقق من عدم تكرار الرقم القومي
    if (empty($errors)) {
        $check_query = "SELECT id FROM customers WHERE national_id = :national_id";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->bindParam(':national_id', $national_id);
        $check_stmt->execute();
        
        if ($check_stmt->fetch()) {
            $errors[] = 'الرقم القومي موجود بالفعل في النظام';
        }
    }
    
    // إدراج البيانات إذا لم توجد أخطاء
    if (empty($errors)) {
        try {
            $query = "INSERT INTO customers (
                name, national_id, account_card, date, start_date, end_date, 
                duration_months, amount, total, permit_number, file_number, 
                transfer_number, phone, phone2, governorate, statement, notes, created_by
            ) VALUES (
                :name, :national_id, :account_card, :date, :start_date, :end_date,
                :duration_months, :amount, :total, :permit_number, :file_number,
                :transfer_number, :phone, :phone2, :governorate, :statement, :notes, :created_by
            )";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':national_id', $national_id);
            $stmt->bindParam(':account_card', $account_card);
            $stmt->bindParam(':date', $date);
            $stmt->bindParam(':start_date', $start_date);
            $stmt->bindParam(':end_date', $end_date);
            $stmt->bindParam(':duration_months', $duration_months);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':total', $total);
            $stmt->bindParam(':permit_number', $permit_number);
            $stmt->bindParam(':file_number', $file_number);
            $stmt->bindParam(':transfer_number', $transfer_number);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':phone2', $phone2);
            $stmt->bindParam(':governorate', $governorate);
            $stmt->bindParam(':statement', $statement);
            $stmt->bindParam(':notes', $notes);
            $stmt->bindParam(':created_by', $_SESSION['user_id']);
            
            if ($stmt->execute()) {
                $customer_id = $db->lastInsertId();

                // معالجة رفع الملفات إذا تم اختيارها
                $file_upload_messages = [];
                if (isset($_FILES['attachments']) && !empty($_FILES['attachments']['name'][0])) {
                    $descriptions = $_POST['file_descriptions'] ?? [];
                    $upload_result = uploadMultipleFiles($_FILES['attachments'], $customer_id, $descriptions);

                    if ($upload_result['success']) {
                        $file_upload_messages[] = "تم رفع {$upload_result['success_count']} من {$upload_result['total']} ملف بنجاح";
                    } else {
                        $file_upload_messages[] = "فشل في رفع الملفات";
                    }
                }

                // تسجيل العملية
                log_activity($_SESSION['user_id'], 'create', 'customers', $customer_id, null, [
                    'name' => $name,
                    'national_id' => $national_id,
                    'amount' => $amount,
                    'total' => $total
                ]);

                $success = true;
                $success_message = 'تم إضافة العميل بنجاح';
                if (!empty($file_upload_messages)) {
                    $success_message .= '<br>' . implode('<br>', $file_upload_messages);
                }
                show_alert($success_message, 'success');

                // إعادة تعيين النموذج
                $_POST = [];
            }
        } catch(Exception $e) {
            $errors[] = 'حدث خطأ في حفظ البيانات: ' . $e->getMessage();
        }
    }
}

// قائمة المحافظات المصرية
$governorates = [
    'القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'البحر الأحمر', 'البحيرة',
    'الفيوم', 'الغربية', 'الإسماعيلية', 'المنوفية', 'المنيا', 'القليوبية',
    'الوادي الجديد', 'السويس', 'أسوان', 'أسيوط', 'بني سويف', 'بورسعيد',
    'دمياط', 'الشرقية', 'جنوب سيناء', 'كفر الشيخ', 'مطروح', 'الأقصر',
    'قنا', 'شمال سيناء', 'سوهاج'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - إضافة عميل جديد</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-users me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="customers.php">
                            <i class="fas fa-users me-1"></i>
                            العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="add_customer.php">
                            <i class="fas fa-user-plus me-1"></i>
                            إضافة عميل
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo $_SESSION['full_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title">
                <i class="fas fa-user-plus me-3"></i>
                إضافة عميل جديد
            </h1>
            <p class="page-subtitle">إدخال بيانات عميل جديد في النظام</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        <?php
        $alert = get_alert();
        if ($alert):
        ?>
        <div class="alert alert-<?php echo $alert['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $alert['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
        <div class="alert alert-danger" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>يرجى تصحيح الأخطاء التالية:</h6>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            نموذج إدخال بيانات العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" id="customerForm" enctype="multipart/form-data">
                            <div class="row">
                                <!-- البيانات الأساسية -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-user me-2"></i>
                                        البيانات الأساسية
                                    </h6>
                                    
                                    <div class="mb-3">
                                        <label for="name" class="form-label">الاسم <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="national_id" class="form-label">الرقم القومي <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="national_id" name="national_id" 
                                               value="<?php echo htmlspecialchars($_POST['national_id'] ?? ''); ?>" 
                                               maxlength="14" pattern="[0-9]{14}" required>
                                        <div class="form-text">14 رقم</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="account_card" class="form-label">الحساب/الكارت <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="account_card" name="account_card" 
                                               value="<?php echo htmlspecialchars($_POST['account_card'] ?? ''); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="governorate" class="form-label">المحافظة</label>
                                        <select class="form-select" id="governorate" name="governorate">
                                            <option value="">اختر المحافظة</option>
                                            <?php foreach ($governorates as $gov): ?>
                                            <option value="<?php echo $gov; ?>" 
                                                    <?php echo (($_POST['governorate'] ?? '') == $gov) ? 'selected' : ''; ?>>
                                                <?php echo $gov; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- التواريخ والمدة -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-calendar me-2"></i>
                                        التواريخ والمدة
                                    </h6>
                                    
                                    <div class="mb-3">
                                        <label for="date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="date" name="date" 
                                               value="<?php echo $_POST['date'] ?? date('Y-m-d'); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">بداية الصرف <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="start_date" name="start_date" 
                                               value="<?php echo $_POST['start_date'] ?? ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">نهاية الصرف <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="end_date" name="end_date" 
                                               value="<?php echo $_POST['end_date'] ?? ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="duration_months" class="form-label">المدة بالشهور <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="duration_months" name="duration_months" 
                                               value="<?php echo $_POST['duration_months'] ?? ''; ?>" min="1" required>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                <!-- المبالغ -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        المبالغ
                                    </h6>
                                    
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="amount" name="amount" 
                                                   value="<?php echo $_POST['amount'] ?? ''; ?>" step="0.01" min="0" required>
                                            <span class="input-group-text">جنيه</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="total" class="form-label">الإجمالي <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="total" name="total" 
                                                   value="<?php echo $_POST['total'] ?? ''; ?>" step="0.01" min="0" required>
                                            <span class="input-group-text">جنيه</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- أرقام الوثائق -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-file-alt me-2"></i>
                                        أرقام الوثائق
                                    </h6>
                                    
                                    <div class="mb-3">
                                        <label for="permit_number" class="form-label">رقم الإذن</label>
                                        <input type="text" class="form-control" id="permit_number" name="permit_number" 
                                               value="<?php echo htmlspecialchars($_POST['permit_number'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="file_number" class="form-label">الملف</label>
                                        <input type="text" class="form-control" id="file_number" name="file_number" 
                                               value="<?php echo htmlspecialchars($_POST['file_number'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="transfer_number" class="form-label">رقم التحويل</label>
                                        <input type="text" class="form-control" id="transfer_number" name="transfer_number" 
                                               value="<?php echo htmlspecialchars($_POST['transfer_number'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <div class="row">
                                <!-- بيانات الاتصال -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-phone me-2"></i>
                                        بيانات الاتصال
                                    </h6>
                                    
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                               pattern="01[0-9]{9}" maxlength="11">
                                        <div class="form-text">مثال: 01234567890</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="phone2" class="form-label">رقم هاتف آخر</label>
                                        <input type="tel" class="form-control" id="phone2" name="phone2" 
                                               value="<?php echo htmlspecialchars($_POST['phone2'] ?? ''); ?>" 
                                               pattern="01[0-9]{9}" maxlength="11">
                                    </div>
                                </div>
                                
                                <!-- البيان والملاحظات -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-sticky-note me-2"></i>
                                        البيان والملاحظات
                                    </h6>
                                    
                                    <div class="mb-3">
                                        <label for="statement" class="form-label">البيان</label>
                                        <textarea class="form-control" id="statement" name="statement" rows="3"><?php echo htmlspecialchars($_POST['statement'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم الملفات المرفقة -->
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-paperclip me-2"></i>
                                        الملفات المرفقة
                                    </h6>

                                    <div class="card border-light">
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="attachments" class="form-label">
                                                    اختر الملفات المراد رفعها
                                                    <small class="text-muted">(اختياري - يمكن رفع عدة ملفات)</small>
                                                </label>
                                                <input type="file" class="form-control" id="attachments" name="attachments[]"
                                                       multiple accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt,.zip,.rar">
                                                <div class="form-text">
                                                    الأنواع المسموحة: صور (JPG, PNG, GIF)، PDF، Word، Excel، نصوص، ملفات مضغوطة
                                                    <br>الحد الأقصى لحجم الملف: 10 ميجابايت
                                                </div>
                                            </div>

                                            <div id="file-preview" class="mt-3" style="display: none;">
                                                <h6>الملفات المختارة:</h6>
                                                <div id="file-list"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                        
                                        <div>
                                            <button type="button" class="btn btn-info me-2" onclick="calculateTotal()">
                                                <i class="fas fa-calculator me-2"></i>
                                                حساب الإجمالي
                                            </button>
                                            
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>
                                                حفظ البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/customer-form.js"></script>

    <script>
        // معاينة الملفات المختارة
        document.getElementById('attachments').addEventListener('change', function(e) {
            const files = e.target.files;
            const preview = document.getElementById('file-preview');
            const fileList = document.getElementById('file-list');

            if (files.length > 0) {
                let html = '';
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const fileSize = formatFileSize(file.size);
                    const fileIcon = getFileIcon(file.type);

                    html += `
                        <div class="d-flex align-items-center mb-2 p-2 border rounded">
                            <i class="${fileIcon} me-2"></i>
                            <div class="flex-grow-1">
                                <strong>${file.name}</strong>
                                <br><small class="text-muted">${fileSize}</small>
                            </div>
                        </div>
                    `;
                }

                fileList.innerHTML = html;
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
            }
        });

        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // الحصول على أيقونة الملف
        function getFileIcon(fileType) {
            const icons = {
                'image/jpeg': 'fas fa-image text-success',
                'image/jpg': 'fas fa-image text-success',
                'image/png': 'fas fa-image text-success',
                'image/gif': 'fas fa-image text-success',
                'application/pdf': 'fas fa-file-pdf text-danger',
                'application/msword': 'fas fa-file-word text-primary',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fas fa-file-word text-primary',
                'application/vnd.ms-excel': 'fas fa-file-excel text-success',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fas fa-file-excel text-success',
                'text/plain': 'fas fa-file-alt text-secondary',
                'application/zip': 'fas fa-file-archive text-warning',
                'application/x-rar-compressed': 'fas fa-file-archive text-warning'
            };

            return icons[fileType] || 'fas fa-file text-secondary';
        }
    </script>
</body>
</html>
