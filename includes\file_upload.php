<?php
/**
 * دوال معالجة رفع الملفات
 * File Upload Handler Functions
 */

/**
 * رفع ملف واحد
 */
function uploadFile($file, $customer_id, $description = '') {
    // التحقق من وجود الملف
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف أو حدث خطأ في الرفع'];
    }
    
    // التحقق من حجم الملف (10MB max)
    $maxSize = 10 * 1024 * 1024; // 10MB
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت'];
    }
    
    // التحقق من نوع الملف
    $allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
        'application/pdf',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'application/zip', 'application/x-rar-compressed'
    ];
    
    $fileType = $file['type'];
    if (!in_array($fileType, $allowedTypes)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح. الأنواع المسموحة: صور، PDF، Word، Excel، نصوص، ZIP'];
    }
    
    // إنشاء اسم ملف فريد
    $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $fileName = 'customer_' . $customer_id . '_' . time() . '_' . uniqid() . '.' . $fileExtension;
    $uploadPath = __DIR__ . '/../uploads/customers/' . $fileName;
    
    // التأكد من وجود المجلد
    $uploadDir = dirname($uploadPath);
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        // حفظ معلومات الملف في قاعدة البيانات
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            $query = "INSERT INTO customer_attachments 
                      (customer_id, file_name, file_path, file_size, file_type, uploaded_by, description) 
                      VALUES (:customer_id, :file_name, :file_path, :file_size, :file_type, :uploaded_by, :description)";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->bindParam(':file_name', $file['name']);
            $stmt->bindParam(':file_path', $fileName);
            $stmt->bindParam(':file_size', $file['size']);
            $stmt->bindParam(':file_type', $fileType);
            $stmt->bindParam(':uploaded_by', $_SESSION['user_id']);
            $stmt->bindParam(':description', $description);
            
            if ($stmt->execute()) {
                return [
                    'success' => true, 
                    'message' => 'تم رفع الملف بنجاح',
                    'file_id' => $db->lastInsertId(),
                    'file_name' => $file['name'],
                    'file_path' => $fileName
                ];
            } else {
                // حذف الملف إذا فشل حفظ البيانات
                unlink($uploadPath);
                return ['success' => false, 'message' => 'فشل في حفظ معلومات الملف'];
            }
        } catch(Exception $e) {
            // حذف الملف إذا حدث خطأ
            if (file_exists($uploadPath)) {
                unlink($uploadPath);
            }
            return ['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()];
        }
    } else {
        return ['success' => false, 'message' => 'فشل في رفع الملف'];
    }
}

/**
 * رفع عدة ملفات
 */
function uploadMultipleFiles($files, $customer_id, $descriptions = []) {
    $results = [];
    $successCount = 0;
    
    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] === UPLOAD_ERR_OK) {
            $file = [
                'name' => $files['name'][$i],
                'type' => $files['type'][$i],
                'tmp_name' => $files['tmp_name'][$i],
                'error' => $files['error'][$i],
                'size' => $files['size'][$i]
            ];
            
            $description = isset($descriptions[$i]) ? $descriptions[$i] : '';
            $result = uploadFile($file, $customer_id, $description);
            $results[] = $result;
            
            if ($result['success']) {
                $successCount++;
            }
        }
    }
    
    return [
        'success' => $successCount > 0,
        'total' => count($files['name']),
        'success_count' => $successCount,
        'results' => $results
    ];
}

/**
 * الحصول على ملفات العميل
 */
function getCustomerFiles($customer_id) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $query = "SELECT ca.*, u.full_name as uploaded_by_name 
                  FROM customer_attachments ca
                  LEFT JOIN users u ON ca.uploaded_by = u.id
                  WHERE ca.customer_id = :customer_id
                  ORDER BY ca.uploaded_at DESC";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->execute();
        
        return $stmt->fetchAll();
    } catch(Exception $e) {
        return [];
    }
}

/**
 * حذف ملف
 */
function deleteFile($file_id, $customer_id) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // الحصول على معلومات الملف
        $query = "SELECT file_path FROM customer_attachments 
                  WHERE id = :file_id AND customer_id = :customer_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':file_id', $file_id);
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->execute();
        
        $file = $stmt->fetch();
        if (!$file) {
            return ['success' => false, 'message' => 'الملف غير موجود'];
        }
        
        // حذف الملف من قاعدة البيانات
        $query = "DELETE FROM customer_attachments 
                  WHERE id = :file_id AND customer_id = :customer_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':file_id', $file_id);
        $stmt->bindParam(':customer_id', $customer_id);
        
        if ($stmt->execute()) {
            // حذف الملف من الخادم
            $filePath = __DIR__ . '/../uploads/customers/' . $file['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            return ['success' => true, 'message' => 'تم حذف الملف بنجاح'];
        } else {
            return ['success' => false, 'message' => 'فشل في حذف الملف'];
        }
    } catch(Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize($bytes) {
    if ($bytes === 0) return '0 بايت';
    
    $k = 1024;
    $sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    $i = floor(log($bytes) / log($k));
    
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

/**
 * الحصول على أيقونة الملف حسب النوع
 */
function getFileIcon($fileType) {
    $icons = [
        'image/jpeg' => 'fas fa-image text-success',
        'image/jpg' => 'fas fa-image text-success',
        'image/png' => 'fas fa-image text-success',
        'image/gif' => 'fas fa-image text-success',
        'application/pdf' => 'fas fa-file-pdf text-danger',
        'application/msword' => 'fas fa-file-word text-primary',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'fas fa-file-word text-primary',
        'application/vnd.ms-excel' => 'fas fa-file-excel text-success',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'fas fa-file-excel text-success',
        'text/plain' => 'fas fa-file-alt text-secondary',
        'application/zip' => 'fas fa-file-archive text-warning',
        'application/x-rar-compressed' => 'fas fa-file-archive text-warning'
    ];
    
    return isset($icons[$fileType]) ? $icons[$fileType] : 'fas fa-file text-secondary';
}
?>
