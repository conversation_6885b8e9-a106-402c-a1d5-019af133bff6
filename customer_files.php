<?php
require_once 'includes/functions.php';
require_once 'includes/file_upload.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    redirect('login.php');
}

$customer_id = $_GET['id'] ?? 0;

if (!$customer_id) {
    redirect('customers.php');
}

// الحصول على بيانات العميل
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT name, national_id FROM customers WHERE id = :customer_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':customer_id', $customer_id);
    $stmt->execute();
    
    $customer = $stmt->fetch();
    
    if (!$customer) {
        redirect('customers.php');
    }
} catch(Exception $e) {
    redirect('customers.php');
}

// معالجة رفع ملف جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['new_file'])) {
    $description = sanitize_input($_POST['description'] ?? '');
    $result = uploadFile($_FILES['new_file'], $customer_id, $description);
    
    if ($result['success']) {
        show_alert('تم رفع الملف بنجاح', 'success');
    } else {
        show_alert($result['message'], 'danger');
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - ملفات العميل</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>
                            <i class="fas fa-paperclip me-2"></i>
                            ملفات العميل: <?php echo htmlspecialchars($customer['name']); ?>
                        </h2>
                        <p class="text-muted mb-0">الرقم القومي: <?php echo htmlspecialchars($customer['national_id']); ?></p>
                    </div>
                    <div>
                        <a href="customers.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للعملاء
                        </a>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-upload me-2"></i>
                            رفع ملف جديد
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-files me-2"></i>
                            الملفات المرفقة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="files-container">
                            <!-- سيتم تحميل الملفات بواسطة AJAX -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal رفع ملف جديد -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">
                        <i class="fas fa-upload me-2"></i>
                        رفع ملف جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="new_file" class="form-label">اختر الملف</label>
                            <input type="file" class="form-control" id="new_file" name="new_file" 
                                   accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt,.zip,.rar" required>
                            <div class="form-text">
                                الأنواع المسموحة: صور، PDF، Word، Excel، نصوص، ملفات مضغوطة (حد أقصى 10 ميجابايت)
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الملف (اختياري)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="أدخل وصفاً للملف..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>
                            رفع الملف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const customerId = <?php echo $customer_id; ?>;
        
        // تحميل الملفات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadFiles();
        });
        
        // تحميل ملفات العميل
        function loadFiles() {
            fetch(`api/customer_files.php?customer_id=${customerId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayFiles(data.files);
                    } else {
                        document.getElementById('files-container').innerHTML = 
                            '<div class="alert alert-warning">لا توجد ملفات مرفقة</div>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('files-container').innerHTML = 
                        '<div class="alert alert-danger">حدث خطأ في تحميل الملفات</div>';
                });
        }
        
        // عرض الملفات
        function displayFiles(files) {
            const container = document.getElementById('files-container');
            
            if (files.length === 0) {
                container.innerHTML = '<div class="alert alert-info">لا توجد ملفات مرفقة</div>';
                return;
            }
            
            let html = '<div class="row">';
            
            files.forEach(file => {
                html += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="${file.file_icon} fa-2x me-3"></i>
                                    <div class="flex-grow-1">
                                        <h6 class="card-title mb-1">${file.file_name}</h6>
                                        <small class="text-muted">${file.file_size}</small>
                                    </div>
                                </div>
                                
                                ${file.description ? `<p class="card-text small">${file.description}</p>` : ''}
                                
                                <div class="small text-muted mb-2">
                                    <i class="fas fa-clock me-1"></i>
                                    ${file.uploaded_at}
                                    <br>
                                    <i class="fas fa-user me-1"></i>
                                    ${file.uploaded_by}
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <a href="${file.download_url}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>
                                        تحميل
                                    </a>
                                    <button class="btn btn-sm btn-danger" onclick="deleteFile(${file.id})">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }
        
        // حذف ملف
        function deleteFile(fileId) {
            if (!confirm('هل أنت متأكد من حذف هذا الملف؟')) {
                return;
            }
            
            fetch('api/customer_files.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `customer_id=${customerId}&file_id=${fileId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadFiles(); // إعادة تحميل الملفات
                    alert('تم حذف الملف بنجاح');
                } else {
                    alert('فشل في حذف الملف: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في حذف الملف');
            });
        }
    </script>
</body>
</html>
