<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PSR1" xsi:noNamespaceSchemaLocation="../../../phpcs.xsd">
    <description>The PSR1 coding standard.</description>

    <!-- 2. Files -->

    <!-- 2.1. PHP Tags -->

    <!-- PHP code MUST use the long <?php ?> tags or the short-echo <?= ?> tags; it MUST NOT use the other tag variations. -->
    <rule ref="Generic.PHP.DisallowAlternativePHPTags"/>
    <rule ref="Generic.PHP.DisallowShortOpenTag"/>
    <rule ref="Generic.PHP.DisallowShortOpenTag.EchoFound">
        <severity>0</severity>
    </rule>

    <!-- 2.2. Character Encoding -->

    <!-- PHP code MUST use only UTF-8 without BOM. -->
    <rule ref="Generic.Files.ByteOrderMark"/>

    <!-- 2.3. Side Effects -->

    <!-- A file SHOULD declare new symbols (classes, functions, constants, etc.) and cause no other side effects, or it SHOULD execute logic with side effects, but SHOULD NOT do both. -->
    <!-- checked by PSR1.Files.SideEffects -->

    <!-- 3. Namespace and Class Names -->

    <!-- Namespaces and classes MUST follow PSR-0.
         This means each class is in a file by itself, and is in a namespace of at least one level: a top-level vendor name. -->
    <!-- checked by PSR1.Classes.ClassDeclaration -->

    <!-- Class names MUST be declared in StudlyCaps. -->
    <rule ref="Squiz.Classes.ValidClassName"/>

    <!-- 4. Class Constants, Properties, and Methods -->

    <!-- 4.1. Constants -->

    <!-- Class constants MUST be declared in all upper case with underscore separators. -->
    <rule ref="Generic.NamingConventions.UpperCaseConstantName"/>

    <!-- 4.3. Methods -->

    <!-- Method names MUST be declared in camelCase(). -->
    <!-- checked by PSR1.Methods.CamelCapsMethodName -->

</ruleset>
