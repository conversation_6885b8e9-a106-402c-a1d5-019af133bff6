<documentation title="Unused function parameters">
    <standard>
    <![CDATA[
    All parameters in a functions signature should be used within the function.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: All the parameters are used.">
        <![CDATA[
function addThree($a, $b, $c)
{
    return <em>$a + $b + $c</em>;
}
        ]]>
        </code>
        <code title="Invalid: One of the parameters is not being used.">
        <![CDATA[
function addThree($a, $b, $c)
{
    return <em>$a + $b</em>;
}
        ]]>
        </code>
    </code_comparison>
</documentation>
