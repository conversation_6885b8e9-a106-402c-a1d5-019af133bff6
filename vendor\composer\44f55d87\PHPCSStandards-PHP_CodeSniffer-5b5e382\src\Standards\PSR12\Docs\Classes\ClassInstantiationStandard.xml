<documentation title="Class Instantiation">
    <standard>
    <![CDATA[
    When instantiating a new class, parenthesis MUST always be present even when there are no arguments passed to the constructor.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Parenthesis used.">
        <![CDATA[
new Foo();
        ]]>
        </code>
        <code title="Invalid: Parenthesis not used.">
        <![CDATA[
new Foo;
        ]]>
        </code>
    </code_comparison>
</documentation>
