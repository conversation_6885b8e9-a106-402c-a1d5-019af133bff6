<documentation title="Opening Function Brace Bsd Allman">
    <standard>
    <![CDATA[
    Function declarations must follow the "BSD/Allman style". The opening brace is on the line
    following the function declaration and is indented to the same column as the start of the
    function declaration. The brace must be the last content on the line.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Opening brace on the next line.">
        <![CDATA[
function fooFunction($arg1, $arg2 = '')
<em>{</em>
    // Do something
}
        ]]>
        </code>
        <code title="Invalid: Opening brace on the same line.">
        <![CDATA[
function fooFunction($arg1, $arg2 = '') <em>{</em>
    // Do something
}
        ]]>
        </code>
    </code_comparison>
</documentation>
