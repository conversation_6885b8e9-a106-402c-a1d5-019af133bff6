<?php
/**
 * API للحصول على بيانات عميل واحد
 * Get Single Customer API
 */

header('Content-Type: application/json; charset=utf-8');
require_once '../includes/functions.php';
require_once '../includes/file_upload.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح بالوصول'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

// التحقق من وجود معرف العميل
$customer_id = (int)($_GET['id'] ?? 0);
if ($customer_id <= 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'معرف العميل مطلوب'
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // استعلام بيانات العميل
    $query = "SELECT 
                c.id, c.name, c.national_id, c.account_card, c.date, 
                c.start_date, c.end_date, c.duration_months, c.amount, c.total,
                c.permit_number, c.file_number, c.transfer_number, 
                c.phone, c.phone2, c.governorate, c.statement, c.notes,
                c.created_at, c.updated_at, c.created_by,
                u.full_name as created_by_name
              FROM customers c
              LEFT JOIN users u ON c.created_by = u.id
              WHERE c.id = :customer_id";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':customer_id', $customer_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $customer = $stmt->fetch();
    
    if (!$customer) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'العميل غير موجود'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    // تنسيق البيانات
    $formatted_customer = [
        'id' => (int)$customer['id'],
        'name' => $customer['name'],
        'national_id' => $customer['national_id'],
        'account_card' => $customer['account_card'],
        'date' => $customer['date'],
        'start_date' => $customer['start_date'],
        'end_date' => $customer['end_date'],
        'duration_months' => (int)$customer['duration_months'],
        'amount' => (float)$customer['amount'],
        'total' => (float)$customer['total'],
        'permit_number' => $customer['permit_number'],
        'file_number' => $customer['file_number'],
        'transfer_number' => $customer['transfer_number'],
        'phone' => $customer['phone'],
        'phone2' => $customer['phone2'],
        'governorate' => $customer['governorate'],
        'statement' => $customer['statement'],
        'notes' => $customer['notes'],
        'created_at' => $customer['created_at'],
        'updated_at' => $customer['updated_at'],
        'created_by' => (int)$customer['created_by'],
        'created_by_name' => $customer['created_by_name'],
        
        // حقول محسوبة
        'is_active' => (new DateTime($customer['end_date']) >= new DateTime()),
        'days_remaining' => max(0, (new DateTime($customer['end_date']))->diff(new DateTime())->days),
        'formatted_amount' => number_format($customer['amount'], 2) . ' جنيه',
        'formatted_total' => number_format($customer['total'], 2) . ' جنيه',
        'formatted_date' => date('d/m/Y', strtotime($customer['date'])),
        'formatted_start_date' => date('d/m/Y', strtotime($customer['start_date'])),
        'formatted_end_date' => date('d/m/Y', strtotime($customer['end_date'])),
        'formatted_created_at' => date('d/m/Y H:i', strtotime($customer['created_at'])),
        'formatted_updated_at' => date('d/m/Y H:i', strtotime($customer['updated_at']))
    ];

    // الحصول على الملفات المرفقة
    $files = getCustomerFiles($customer_id);
    $formatted_files = [];
    foreach ($files as $file) {
        $formatted_files[] = [
            'id' => $file['id'],
            'file_name' => $file['file_name'],
            'file_size' => formatFileSize($file['file_size']),
            'file_type' => $file['file_type'],
            'file_icon' => getFileIcon($file['file_type']),
            'description' => $file['description'],
            'uploaded_at' => date('d/m/Y H:i', strtotime($file['uploaded_at'])),
            'uploaded_by' => $file['uploaded_by_name'],
            'download_url' => 'download_file.php?id=' . $file['id']
        ];
    }

    echo json_encode([
        'success' => true,
        'customer' => $formatted_customer,
        'files' => $formatted_files
    ], JSON_UNESCAPED_UNICODE);
    
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في الخادم: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
