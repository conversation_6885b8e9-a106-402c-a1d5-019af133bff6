<?php
/**
 * تحميل الملفات المرفقة
 * Download Attached Files
 */

require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    header('Location: login.php');
    exit;
}

$file_id = $_GET['id'] ?? 0;

if (!$file_id) {
    header('HTTP/1.0 404 Not Found');
    echo 'الملف غير موجود';
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // الحصول على معلومات الملف
    $query = "SELECT ca.*, c.name as customer_name 
              FROM customer_attachments ca
              JOIN customers c ON ca.customer_id = c.id
              WHERE ca.id = :file_id";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':file_id', $file_id);
    $stmt->execute();
    
    $file = $stmt->fetch();
    
    if (!$file) {
        header('HTTP/1.0 404 Not Found');
        echo 'الملف غير موجود';
        exit;
    }
    
    $file_path = __DIR__ . '/uploads/customers/' . $file['file_path'];
    
    // التحقق من وجود الملف على الخادم
    if (!file_exists($file_path)) {
        header('HTTP/1.0 404 Not Found');
        echo 'الملف غير موجود على الخادم';
        exit;
    }
    
    // تسجيل عملية التحميل
    log_activity($_SESSION['user_id'], 'download', 'customer_attachments', $file_id, null, [
        'file_name' => $file['file_name'],
        'customer_name' => $file['customer_name']
    ]);
    
    // إعداد headers للتحميل
    header('Content-Type: ' . $file['file_type']);
    header('Content-Disposition: attachment; filename="' . $file['file_name'] . '"');
    header('Content-Length: ' . $file['file_size']);
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    
    // قراءة وإرسال الملف
    readfile($file_path);
    exit;
    
} catch(Exception $e) {
    header('HTTP/1.0 500 Internal Server Error');
    echo 'خطأ في تحميل الملف: ' . $e->getMessage();
    exit;
}
?>
