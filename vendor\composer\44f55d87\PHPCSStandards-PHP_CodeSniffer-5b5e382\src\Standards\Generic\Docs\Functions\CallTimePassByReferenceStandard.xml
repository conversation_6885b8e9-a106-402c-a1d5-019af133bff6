<documentation title="Call-Time Pass-By-Reference">
    <standard>
    <![CDATA[
    Call-time pass-by-reference is not allowed. It should be declared in the function definition.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Pass-by-reference is specified in the function definition.">
        <![CDATA[
function foo(<em>&</em>$bar)
{
    $bar++;
}

$baz = 1;
foo($baz);
        ]]>
        </code>
        <code title="Invalid: Pass-by-reference is done in the call to a function.">
        <![CDATA[
function foo($bar)
{
    $bar++;
}

$baz = 1;
foo(<em>&</em>$baz);
        ]]>
        </code>
    </code_comparison>
</documentation>
