<?php
/**
 * API لإدارة ملفات العميل
 * Customer Files Management API
 */

header('Content-Type: application/json; charset=utf-8');
require_once '../includes/functions.php';
require_once '../includes/file_upload.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح بالوصول'
    ]);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$customer_id = $_GET['customer_id'] ?? $_POST['customer_id'] ?? 0;

if (!$customer_id) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'معرف العميل مطلوب'
    ]);
    exit;
}

switch ($method) {
    case 'GET':
        // الحصول على ملفات العميل
        $files = getCustomerFiles($customer_id);
        
        // تنسيق البيانات للعرض
        $formatted_files = [];
        foreach ($files as $file) {
            $formatted_files[] = [
                'id' => $file['id'],
                'file_name' => $file['file_name'],
                'file_size' => formatFileSize($file['file_size']),
                'file_type' => $file['file_type'],
                'file_icon' => getFileIcon($file['file_type']),
                'description' => $file['description'],
                'uploaded_at' => date('Y-m-d H:i', strtotime($file['uploaded_at'])),
                'uploaded_by' => $file['uploaded_by_name'],
                'download_url' => 'download_file.php?id=' . $file['id']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'files' => $formatted_files
        ]);
        break;
        
    case 'POST':
        // رفع ملف جديد
        if (!isset($_FILES['file'])) {
            echo json_encode([
                'success' => false,
                'message' => 'لم يتم اختيار ملف'
            ]);
            exit;
        }
        
        $description = $_POST['description'] ?? '';
        $result = uploadFile($_FILES['file'], $customer_id, $description);
        
        echo json_encode($result);
        break;
        
    case 'DELETE':
        // حذف ملف
        parse_str(file_get_contents("php://input"), $data);
        $file_id = $data['file_id'] ?? 0;
        
        if (!$file_id) {
            echo json_encode([
                'success' => false,
                'message' => 'معرف الملف مطلوب'
            ]);
            exit;
        }
        
        $result = deleteFile($file_id, $customer_id);
        echo json_encode($result);
        break;
        
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'طريقة غير مدعومة'
        ]);
}
?>
